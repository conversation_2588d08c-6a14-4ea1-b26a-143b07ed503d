"""
Get Work Order assign excel file 

assign work order from old EMS
"""

import pandas as pd
import os
import time
import pyperclip
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import Action<PERSON>hains

# Set up logging
import logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Define the path to the Excel file
excel_file_path = os.path.join(os.path.dirname(__file__), 'Work Order Assign.xlsx')

# Load the Excel file
df = pd.read_excel(excel_file_path)
logging.info(f"Successfully loaded {excel_file_path}")
logging.info(f"Found {len(df)} work orders to process")

# Initialize WebDriver
options = webdriver.ChromeOptions()
options.add_experimental_option("detach", True)  # Keep browser open for debugging

# Set up the Chrome driver
driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)
driver.maximize_window()

# Navigate to the EMS website
logging.info("Opening EMS website...")
driver.get('https://ems-lgensol.singlex.com/maximo/ui/?event=loadapp&value=wotrack&uisessionid=1227&_tt=lal3vb55oldrv7s73k3k32p91l')

# Wait for user to log in manually if needed
input("Please log in to the website if required, then press Enter to continue...")

# Process each row in the Excel file
for index, row in df.iterrows():
    time.sleep(3)
    work_order_number = str(row['Work Order No.']).strip()
    logging.info(f"Processing row {index+1}/{len(df)}: Work Order {work_order_number}")

    # Find the search input field
    search_field = WebDriverWait(driver, 30).until(
        EC.element_to_be_clickable((By.XPATH, '/html/body/form/div/table[2]/tbody/tr/td/table/tbody/tr/td/table/tbody/tr/td/table/tbody/tr[1]/td/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td[2]/table/tbody/tr/td/div/input'))
    )
    
    # Clear any existing text
    search_field.clear()
    
    # Enter the work order number
    search_field.send_keys(work_order_number)
    logging.info(f"Entered Work Order: {work_order_number}")
    
    # Press Enter to search
    search_field.send_keys(Keys.ENTER)
    logging.info("Submitted search")
    # time.sleep(3)
    
    # go to assignments tab
    assignments_tab = WebDriverWait(driver, 30).until(
        EC.element_to_be_clickable((By.XPATH, '/html/body/form/div/table[2]/tbody/tr/td/table/tbody/tr/td/table/tbody/tr/td/table/tbody/tr[2]/td/div/table/tbody/tr/td/div/table/tbody/tr/td/table/tbody/tr/td[3]/div/table/tbody/tr/td/table/tbody/tr[1]/td/ul/li[4]/a'))
    )
    assignments_tab.click()
    logging.info("Clicked Assignments tab")
    # time.sleep(3)
    
    # Find the input field with the value to copy
    input_field = WebDriverWait(driver, 15).until(
        EC.presence_of_element_located((By.XPATH, '/html/body/form/div/table[2]/tbody/tr/td/table/tbody/tr/td/table/tbody/tr/td/table/tbody/tr[2]/td/div/table/tbody/tr/td/div/table/tbody/tr/td/table/tbody/tr/td[3]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr[2]/td/table/tbody/tr[2]/td/table/tbody/tr/td/div/table/tbody/tr[3]/td/table/tbody/tr[2]/td/table/tbody/tr[4]/td[7]/input'))
    )
    
    # Get the value from the input field
    value_to_copy = input_field.get_attribute('value')
    
    # Copy to clipboard using pyperclip
    pyperclip.copy(value_to_copy)
    
    # Log the copied value
    logging.info(f"Copied value: {value_to_copy}")
    
    # Store in the DataFrame
    row['Copied Value'] = value_to_copy
    
    # # Select the text and copy it using keyboard shortcuts
    # input_field.click()
    # actions = ActionChains(driver)
    # actions.key_down(Keys.CONTROL).send_keys('a').key_up(Keys.CONTROL).perform()
    # time.sleep(0.5)
    # actions.key_down(Keys.CONTROL).send_keys('c').key_up(Keys.CONTROL).perform()
    # logging.info("Used keyboard shortcuts to copy the value")
    # time.sleep(1)

    # click add row
    add_row_button = WebDriverWait(driver, 30).until(
        EC.element_to_be_clickable((By.ID, 'm6798a95d_bg_button_addrow-pb'))
    )
    add_row_button.click()
    logging.info("Clicked Add Row button")
    time.sleep(3)

    # search button
    search_button = WebDriverWait(driver, 30).until(
        EC.element_to_be_clickable((By.XPATH, '/html/body/form/div/table[2]/tbody/tr/td/table/tbody/tr/td/table/tbody/tr/td/table/tbody/tr[2]/td/div/table/tbody/tr/td/div/table/tbody/tr/td/table/tbody/tr/td[3]/div/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr/td/table/tbody/tr[2]/td/table/tbody/tr[2]/td/table/tbody/tr[2]/td/table/tbody/tr/td/div/table/tbody/tr[3]/td/table/tbody/tr[2]/td/table/tbody/tr[5]/td[7]/img'))
    )
    search_button.click()
    logging.info("Clicked Search button")
    time.sleep(3)

    # clear field
    input_field = WebDriverWait(driver, 15).until(
        EC.presence_of_element_located((By.XPATH, '/html/body/form/div/table[3]/tbody/tr/td[3]/table/tbody/tr[2]/td/table/tbody/tr/td/table[2]/tbody/tr/td/div/table/tbody/tr/td/div/table/tbody/tr[3]/td/table/tbody/tr[2]/td/table/tbody/tr[2]/td[5]/input'))
    )
    input_field.clear()
    logging.info("Cleared input field")
    time.sleep(3)

    # paste value
    input_field = WebDriverWait(driver, 15).until(
        EC.element_to_be_clickable((By.XPATH, '/html/body/form/div/table[3]/tbody/tr/td[3]/table/tbody/tr[2]/td/table/tbody/tr/td/table[2]/tbody/tr/td/div/table/tbody/tr/td/div/table/tbody/tr[3]/td/table/tbody/tr[2]/td/table/tbody/tr[2]/td[2]/input'))
    )
    input_field.click()
    actions = ActionChains(driver)
    actions.key_down(Keys.CONTROL).send_keys('a').key_up(Keys.CONTROL).perform()
    time.sleep(0.5)
    actions.key_down(Keys.CONTROL).send_keys('v').key_up(Keys.CONTROL).perform()
    logging.info("Pasted value")
    input_field.send_keys(Keys.ENTER)
    logging.info("Submitted search")
    time.sleep(3)

    # double click first result
    first_result = WebDriverWait(driver, 15).until(
        EC.element_to_be_clickable((By.XPATH, '/html/body/form/div/table[3]/tbody/tr/td[3]/table/tbody/tr[2]/td/table/tbody/tr/td/table[2]/tbody/tr/td/div/table/tbody/tr/td/div/table/tbody/tr[3]/td/table/tbody/tr[2]/td/table/tbody/tr[4]/td[2]/span'))
    )
    actions = ActionChains(driver)
    actions.double_click(first_result).perform()
    logging.info("Double clicked first result")
    time.sleep(3)

    # click save
    save_button = WebDriverWait(driver, 30).until(
        EC.element_to_be_clickable((By.XPATH, '/html/body/form/div/table[2]/tbody/tr/td/table/tbody/tr/td/table/tbody/tr/td/table/tbody/tr[2]/td/div/table/tbody/tr/td/div/table/tbody/tr/td/table/tbody/tr/td[3]/div/table/tbody/tr/td/table/tbody/tr[1]/td/table/tbody/tr/td[1]/div/table/tbody/tr/td[2]'))
    )
    save_button.click()
    logging.info("Clicked save button")
    time.sleep(3)

    # NO button for confirmation
    confirm_button = WebDriverWait(driver, 30).until(
        EC.element_to_be_clickable((By.XPATH, '/html/body/form/div/table[3]/tbody/tr/td[3]/table/tbody/tr[2]/td/table/tbody/tr/td/table[3]/tbody/tr/td/table/tbody/tr/td/div/table/tbody/tr/td/button[1]'))
    )
    confirm_button.click()
    logging.info("Clicked confirm button")
    time.sleep(4)
    
    # Wait for user confirmation before proceeding to next work order
    # input(f"Processed Work Order {work_order_number}. Press Enter to continue to next...")

# Ask if the user wants to close the browser
close_browser = input("Do you want to close the browser? (Y/N): ")
if close_browser.upper() == 'Y':
    driver.quit()
    logging.info("Browser closed.")
else:
    logging.info("Browser left open for inspection.")
