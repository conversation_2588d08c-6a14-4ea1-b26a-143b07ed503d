import requests
import urllib3
from urllib.parse import urlencode
import pandas as pd

MAXIMO_URL = "https://ems-lgensol.singlex.com/maximo/oslc/os/oslcwodetail"
API_KEY    = "ar06omth2ds6js8lt26god1nvhhdp8h60savkcla"

headers = {
    "apikey": API_KEY,
    "Accept": "application/json"
}

# Basic working query
where_clause_basic = (
    'spi_wm:istask=0 and '
    'spi_wm:woclass="WORKORDER" and '
    'spi_wm:siteid="UTIL.GM" and '
    'spi_wm:ownergroup="GM.UT.U"'
)

# Extended query with additional filters (when ready to use)
where_clause_extended = (
    'spi_wm:istask=0 and '
    'spi_wm:woclass="WORKORDER" and '
    'spi_wm:siteid="UTIL.GM" and '
    'spi_wm:ownergroup="GM.UT.U" and '
    'spi_wm:zinfowoflag=0 and '  # Added proper prefix
    'spi_wm:status="DRAFT" and '  # OSLC syntax for status filter
    'not(spi_wm:worktype="DM")'  # OSLC syntax for not equal
)

# Working query with zinfowoflag
where_clause_working = (
    'spi_wm:istask=0 and '
    'spi_wm:woclass="WORKORDER" and '
    'spi_wm:siteid="UTIL.GM" and '
    'spi_wm:ownergroup="GM.UT.U" and '
    'dcterms:created>"2025-08-14T05:03:33+00:00"'
)

# Use the working query
where_clause = where_clause_working

params = {
    "oslc.where": where_clause,
    "oslc.select": "*",  # or specific fields: "spi:wostatus,spi_wm:actuallabor,spi_wm:statusdate,spi_wm:jpnum,dcterms:identifier,dcterms:title"
    "lean": "1",
}

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

full_url = f"{MAXIMO_URL}?{urlencode(params)}"
print(f"🌐 Request URL: {full_url}\n")

resp = requests.get(MAXIMO_URL, headers=headers, params=params, verify=False)

if resp.status_code != 200:
    print(f"❌ Request failed: {resp.status_code}")
    print(resp.text)
else:
    data = resp.json()
    records = data.get("member", [])
    print(f"✅ Data retrieved successfully from Maximo")
    print(f"🔍 {len(records)} work orders returned\n")

    if records:
        df = pd.json_normalize(records)
        output_file = "maximo_workorders.xlsx"
        df.to_excel(output_file, index=False)
        print(f"💾 Data saved to {output_file}")
        
        # Print column names and sample values to debug field names
        print(f"📋 Available columns: {list(df.columns)}")
        
        # Look for status and worktype related fields
        status_cols = [col for col in df.columns if 'status' in col.lower()]
        worktype_cols = [col for col in df.columns if 'work' in col.lower() and 'type' in col.lower()]
        
        if status_cols:
            print(f"🔍 Status-related columns: {status_cols}")
            for col in status_cols[:2]:  # Show first 2 status columns
                try:
                    # Handle columns that might contain lists or complex data
                    if df[col].dtype == 'object':
                        # Check if column contains lists
                        sample_values = []
                        for val in df[col].head(5):
                            if isinstance(val, list):
                                sample_values.append(f"[list with {len(val)} items]")
                            else:
                                sample_values.append(val)
                        print(f"   {col}: {sample_values}")
                    else:
                        print(f"   {col}: {df[col].unique()[:5].tolist()}")
                except Exception as e:
                    print(f"   {col}: [Error displaying values: {str(e)}]")
        
        if worktype_cols:
            print(f"🔍 Worktype-related columns: {worktype_cols}")
            for col in worktype_cols[:2]:  # Show first 2 worktype columns  
                try:
                    # Handle columns that might contain lists or complex data
                    if df[col].dtype == 'object':
                        # Check if column contains lists
                        sample_values = []
                        for val in df[col].head(5):
                            if isinstance(val, list):
                                sample_values.append(f"[list with {len(val)} items]")
                            else:
                                sample_values.append(val)
                        print(f"   {col}: {sample_values}")
                    else:
                        print(f"   {col}: {df[col].unique()[:5].tolist()}")
                except Exception as e:
                    print(f"   {col}: [Error displaying values: {str(e)}]")
    else:
        print("📝 No records found matching the criteria")