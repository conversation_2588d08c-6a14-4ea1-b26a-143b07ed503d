import pandas as pd
import os
import time
import pyperclip
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Set up logging
import logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Define the path to the Excel file
excel_file_path = os.path.join(os.path.dirname(__file__), 'Create work order to draft.xlsx')

# Load the Excel file
try:
    df = pd.read_excel(excel_file_path)
    logging.info(f"Successfully loaded {excel_file_path}")
    logging.info(f"Found {len(df)} work orders to create")
except Exception as e:
    logging.error(f"Error loading Excel file: {e}")
    exit(1)

# Initialize WebDriver
options = webdriver.ChromeOptions()
options.add_experimental_option("detach", True)  # Keep browser open for debugging

try:
    # Set up the Chrome driver
    driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)
    driver.maximize_window()

    # Navigate to the EMS website
    logging.info("Opening EMS website...")
    driver.get('https://ems2-ensol.singlex.com/')

    # Wait for user to log in manually if needed
    input("Please log in to the website if required, then press Enter to continue...")

    # Click on the sidebar menu
    logging.info("Navigating to Work Order creation...")
    sidebar_menu = WebDriverWait(driver, 30).until(
        EC.element_to_be_clickable((By.XPATH, '//*[@id="sidebarScrollBar"]/div[2]/ul/li[3]/div[1]/h5'))
    )
    sidebar_menu.click()

    # Click on the Work Order submenu
    work_order_menu = WebDriverWait(driver, 30).until(
        EC.element_to_be_clickable((By.XPATH, '//*[@id="sidebarScrollBar"]/div[2]/ul/li[3]/div[2]/ul/li[2]/a/span'))
    )
    work_order_menu.click()
    time.sleep(3)

    # Process each row in the Excel file
    for index, row in df.iterrows():
        try:
            logging.info(f"Processing row {index+1}/{len(df)}")

            # create work order. Click 'NEW'
            print("Creating work order...")
            create_button = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '/html/body/div[1]/div[2]/section/div/div[2]/div[2]/div/section/div/div[3]/div[2]/button[1]'))
            )
            create_button.click()
            time.sleep(3)

            # fill in work type
            work_type = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '/html/body/div[1]/div[2]/section/div[1]/div[2]/div[2]/div[2]/div[2]/div[2]/div/section[2]/div[2]/div/div/table/tbody/tr[2]/td[2]/div/div/div/div/div/input'))
            )
            work_type.send_keys("CM")
            work_type.send_keys(Keys.ENTER)
            time.sleep(3)

            # fill in work order title
            work_order_title = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '/html/body/div[1]/div[2]/section/div/div[2]/div[2]/div[2]/div[2]/div[2]/div/section[2]/div[2]/div/div/table/tbody/tr[2]/td[3]/div/input'))
            )
            work_order_title.send_keys(row['Work Order Title'])
            time.sleep(3)

            # select shut down type
            shutdown_type = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '/html/body/div[1]/div[2]/section/div/div[2]/div[2]/div[2]/div[2]/div[2]/div/section[3]/div[2]/div/div/div[1]/table/tbody/tr[1]/td[1]/div/div/div/div/div/input'))
            )
            shutdown_type.send_keys("R")
            shutdown_type.send_keys(Keys.ENTER)
            shutdown_type.send_keys("R")
            shutdown_type.send_keys(Keys.ENTER)
            time.sleep(3)            
            shutdown_type.send_keys("R")
            shutdown_type.send_keys(Keys.ENTER)
            time.sleep(3)

            # fill in equipment ID
            equipment_id = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '/html/body/div[1]/div[2]/section/div/div[2]/div[2]/div[2]/div[2]/div[2]/div/section[2]/div[2]/div/div/table/tbody/tr[3]/td[1]/div/div[1]/div/div/input'))
            )
            equipment_id.send_keys(row['Equipment ID'])
            time.sleep(3)
            equipment_id.send_keys(Keys.ENTER)
            time.sleep(3)

            # # Wait for the checkbox element to be clickable
            # first_check_box = WebDriverWait(driver, 30).until(
            #     EC.element_to_be_clickable((
            #         By.XPATH,
            #         '/html/body/div[1]/div[2]/section/div/div[2]/div[2]/div[3]/div[2]/div[2]/div/section/div/div[2]/div/div[8]/div[1]/div[1]/div[2]/div[1]'
            #     ))
            # )
            # time.sleep(3)

            # # Create an action chain to double-click
            # actions = ActionChains(driver)
            # actions.double_click(first_check_box).perform()

            # fill in plan start and end date
            plan_start_date = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '/html/body/div[1]/div[2]/section/div/div[2]/div[2]/div[2]/div[2]/div[2]/div/section[3]/div[2]/div/div/div[1]/table/tbody/tr[1]/td[3]/div/div/input'))
            )
            copy = pyperclip.copy(row['work plan date'])
            plan_start_date.send_keys(Keys.CONTROL, 'v')
            time.sleep(3)

            # fill in OP Name
            op_name = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '/html/body/div[1]/div[2]/section/div/div[2]/div[2]/div[2]/div[2]/div[2]/div/section[3]/div[2]/div/div/div[2]/div/section/div/div[1]/div[12]/div[1]/div[1]/div[2]/div[3]/div/input'))
            )
            op_name.send_keys(row['Work Order Title'])
            time.sleep(3)

            # fill in detail
            detail = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '/html/body/div[1]/div[2]/section/div/div[2]/div[2]/div[2]/div[2]/div[2]/div/section[3]/div[2]/div/div/div[2]/div/div[2]/table/tbody/tr/td/div/textarea'))
            )
            detail.send_keys(row['detail'])
            time.sleep(3)


            # Step 1: Double-click the duration cell to activate the input
            duration_cell_xpath = '/html/body/div[1]/div[2]/section/div/div[2]/div[2]/div[2]/div[2]/div[2]/div/section[3]/div[2]/div/div/div[2]/div/section/div/div[1]/div[12]/div[1]/div[1]/div[2]/div[9]'

            duration_cell = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, duration_cell_xpath))
            )

            actions = ActionChains(driver)
            actions.double_click(duration_cell).perform()

            # Optional: small wait to let the input show
            time.sleep(1)

            # Step 2: Enter duration into the input box
            duration_input_xpath = duration_cell_xpath + '/input'

            duration_input = WebDriverWait(driver, 10).until(
                EC.visibility_of_element_located((By.XPATH, duration_input_xpath))
            )

            duration_input.clear()
            duration_input.send_keys(str(row['duration']))
            duration_input.send_keys(Keys.ENTER)

            # Optional: wait for input to register
            time.sleep(1)

            # save work order
            save_button = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '/html/body/div[1]/div[2]/section/div/div[2]/div[2]/div[2]/div[2]/div[3]/div/button[1]'))
            )
            save_button.click()
            time.sleep(3)
            # press ok
            ok_button = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '/html/body/div[8]/div/div/div/div[3]/div/button'))
            )
            ok_button.click()
            time.sleep(3)

            # press close
            close_button = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '/html/body/div[1]/div[2]/section/div/div[2]/div[2]/div[2]/div[2]/div[3]/div/button[4]'))
            )
            close_button.click()
            time.sleep(3)


        except Exception as e:
            logging.error(f"Error processing row {index+1}: {e}")
            continue

    # Save the updated DataFrame back to Excel
    df.to_excel(excel_file_path, index=False)
    logging.info(f"Successfully saved work order numbers to {excel_file_path}")

except Exception as e:
    logging.error(f"An error occurred: {e}")

finally:
    # Ask if the user wants to close the browser
    close_browser = input("Do you want to close the browser? (Y/N): ")
    if close_browser.upper() == 'Y':
        driver.quit()
        logging.info("Browser closed.")
    else:
        logging.info("Browser left open for inspection.")
