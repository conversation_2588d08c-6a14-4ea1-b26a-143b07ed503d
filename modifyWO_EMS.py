import pandas as pd
import os
import time
import pyperclip
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Set up logging
import logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Define the path to the Excel file
excel_file_path = os.path.join(os.path.dirname(__file__), 'Create work order to draft.xlsx')

# Load the Excel file
try:
    df = pd.read_excel(excel_file_path)
    logging.info(f"Successfully loaded {excel_file_path}")
    logging.info(f"Found {len(df)} work orders to create")
except Exception as e:
    logging.error(f"Error loading Excel file: {e}")
    exit(1)

# Initialize WebDriver
options = webdriver.ChromeOptions()
options.add_experimental_option("detach", True)  # Keep browser open for debugging

try:
    # Set up the Chrome driver
    driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)
    driver.maximize_window()

    # Navigate to the EMS website
    logging.info("Opening EMS website...")
    driver.get('https://ems2-ensol.singlex.com/')

    # Wait for user to log in manually if needed
    input("Please log in to the website if required, then press Enter to continue...")

    # Click on the sidebar menu
    logging.info("Navigating to Work Order creation...")
    sidebar_menu = WebDriverWait(driver, 30).until(
        EC.element_to_be_clickable((By.XPATH, '//*[@id="sidebarScrollBar"]/div[2]/ul/li[3]/div[1]/h5'))
    )
    sidebar_menu.click()

    # Click on the Work Order submenu
    work_order_menu = WebDriverWait(driver, 30).until(
        EC.element_to_be_clickable((By.XPATH, '//*[@id="sidebarScrollBar"]/div[2]/ul/li[3]/div[2]/ul/li[2]/a/span'))
    )
    work_order_menu.click()
    time.sleep(30)

    # Process each row in the Excel file
    for index, row in df.iterrows():
        try:
            logging.info(f"Processing row {index+1}/{len(df)}")

            work_order_no = str(df['Work Order No.'][index]).strip()
            print(f"Modify Work order- {work_order_no}...")

            modify_wo = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '/html/body/div[1]/div[2]/section/div/div[2]/div[2]/div/div[3]/div[1]/div[1]/div[1]/div/input'))
            )

            driver.execute_script("arguments[0].focus();", modify_wo)
            modify_wo.clear()
            time.sleep(0.3)  # allow focus to settle
            modify_wo.send_keys(work_order_no)
            modify_wo.send_keys(Keys.ENTER)
            time.sleep(10)

            
            xpath = '/html/body/div[1]/div[2]/section/div/div[2]/div[2]/div/section/div/div[2]/div/div[31]/div[1]/div[1]/div[2]/div[1]/div/a'

            # --- Click element via JS to avoid interception ---
            elements = driver.find_elements(By.XPATH, xpath)
            if elements and elements[0].is_displayed():
                driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", elements[0])
                time.sleep(0.3)
                driver.execute_script("arguments[0].click();", elements[0])
                print("✅ Clicked element via JS")
                time.sleep(2)

            # fill in equipment ID
            equipment_id = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '/html/body/div[1]/div[2]/section/div/div[2]/div[2]/div[2]/div[2]/div[2]/div/section[2]/div[2]/div/div/table/tbody/tr[3]/td[1]/div/div[1]/div/div/input'))
            )
            equipment_id.clear()
            equipment_id.send_keys(row['Equipment ID'])
            time.sleep(3)
            equipment_id.send_keys(Keys.ENTER)
            time.sleep(3)
            # =================================
            # save work order
            save_button = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '/html/body/div[1]/div[2]/section/div/div[2]/div[2]/div[2]/div[2]/div[3]/div/button[1]'))
            )
            save_button.click()
            time.sleep(3)
            # press ok
            ok_button = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '/html/body/div[8]/div/div/div/div[3]/div/button'))
            )
            ok_button.click()
            time.sleep(3)

            # press close
            close_button = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '/html/body/div[1]/div[2]/section/div/div[2]/div[2]/div[2]/div[2]/div[3]/div/button[4]'))
            )
            close_button.click()
            time.sleep(3)


        except Exception as e:
            logging.error(f"Error processing row {index+1}: {e}")
            continue

    # Save the updated DataFrame back to Excel
    df.to_excel(excel_file_path, index=False)
    logging.info(f"Successfully saved work order numbers to {excel_file_path}")

except Exception as e:
    logging.error(f"An error occurred: {e}")

finally:
    # Ask if the user wants to close the browser
    close_browser = input("Do you want to close the browser? (Y/N): ")
    if close_browser.upper() == 'Y':
        driver.quit()
        logging.info("Browser closed.")
    else:
        logging.info("Browser left open for inspection.")
