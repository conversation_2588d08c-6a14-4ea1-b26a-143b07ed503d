import pandas as pd
import os
import time
import pyperclip
import logging
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import (
    TimeoutException,
    NoSuchElementException,
    StaleElementReferenceException,
    ElementClickInterceptedException,
    ElementNotInteractableException,
)

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Define the path to the Excel file
excel_file_path = os.path.join(os.path.dirname(__file__), 'Work Order.xlsx')

# Load the Excel file
try:
    df = pd.read_excel(excel_file_path)
    logging.info(f"Successfully loaded {excel_file_path}")
    logging.info(f"Found {len(df)} work orders to create")
except Exception as e:
    logging.error(f"Error loading Excel file: {e}")
    exit(1)

# Helper utilities
def screenshot_on_error(driver, prefix="error"):
    ts = datetime.now().strftime("%Y%m%d_%H%M%S")
    name = f"{prefix}_{ts}.png"
    try:
        driver.save_screenshot(name)
        logging.info(f"Saved screenshot: {name}")
    except Exception as e:
        logging.warning(f"Failed to save screenshot: {e}")

def wait_for_clickable(driver, locator, timeout=30):
    return WebDriverWait(driver, timeout).until(EC.element_to_be_clickable(locator))

def wait_for_presence(driver, locator, timeout=30):
    return WebDriverWait(driver, timeout).until(EC.presence_of_element_located(locator))

def safe_click(driver, locator, timeout=30, max_attempts=3, sleep_between=0.5):
    """Find element and click, retry on common transient exceptions."""
    last_exc = None
    for attempt in range(1, max_attempts + 1):
        try:
            el = WebDriverWait(driver, timeout).until(EC.element_to_be_clickable(locator))
            # Try normal click; fallback to JS click if intercepted
            try:
                el.click()
            except (ElementClickInterceptedException, ElementNotInteractableException, StaleElementReferenceException):
                # attempt JavaScript click as fallback
                driver.execute_script("arguments[0].click();", el)
            return el
        except (TimeoutException, StaleElementReferenceException, ElementClickInterceptedException, NoSuchElementException) as exc:
            last_exc = exc
            logging.debug(f"safe_click attempt {attempt} failed for {locator}: {exc}")
            time.sleep(sleep_between)
    raise last_exc

def safe_send_keys(driver, locator, text, clear_first=True, timeout=30, max_attempts=3, sleep_between=0.3):
    last_exc = None
    for attempt in range(1, max_attempts + 1):
        try:
            el = WebDriverWait(driver, timeout).until(EC.element_to_be_clickable(locator))
            if clear_first:
                try:
                    el.clear()
                except Exception:
                    # fallback using JS to clear
                    driver.execute_script("arguments[0].value = '';", el)
            el.send_keys(text)
            return el
        except (TimeoutException, StaleElementReferenceException, ElementNotInteractableException) as exc:
            last_exc = exc
            logging.debug(f"safe_send_keys attempt {attempt} failed for {locator}: {exc}")
            time.sleep(sleep_between)
    raise last_exc

# Initialize WebDriver
options = webdriver.ChromeOptions()
options.add_experimental_option("detach", True)  # Keep browser open for debugging

try:
    # Set up the Chrome driver
    driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)
    driver.maximize_window()

    # Navigate to the EMS website
    logging.info("Opening EMS website...")
    driver.get('https://ems-lgensol.singlex.com/maximo/')

    # Wait for user to log in manually if needed
    input("Please log in to the website if required, then press Enter to continue...")

    # click work order page
    safe_click(driver, (By.XPATH, '//*[@id="FavoriteApp_WOTRACK"]'), timeout=30)
    logging.info("Navigated to Work Order page")

    # process each row with per-row retries
    for index, row in df.iterrows():
        work_order = str(row['Work Order No.'])
        labor_name_text = str(row['Labor Name']) if 'Labor Name' in row else ""

        logging.info(f"Processing row {index} - Work Order: {work_order}")
        row_attempts = 3
        success = False

        for attempt in range(1, row_attempts + 1):
            try:
                logging.info(f" Row attempt {attempt} for WO {work_order}")

                # 1) search field - clear and type
                search_locator = (By.XPATH, '//*[@id="quicksearch"]')
                safe_send_keys(driver, search_locator, work_order, clear_first=True, timeout=30)

                # Press Enter to search
                try:
                    search_el = wait_for_clickable(driver, search_locator, timeout=10)
                    search_el.send_keys(Keys.ENTER)
                except Exception:
                    # if element disappears quickly, do JS Enter
                    driver.execute_script("arguments[0].dispatchEvent(new KeyboardEvent('keydown', {'key':'Enter'}));", search_el)

                # 2) wait for Actuals tab to appear (indicates WO loaded)
                assignments_tab_locator = (By.XPATH, '//*[@id="m272f5640-tab_anchor"]')
                # Wait longer; sometimes search result page takes time to render
                safe_click(driver, assignments_tab_locator, timeout=30)
                logging.info(" Clicked Actuals tab")

                # click drop down button
                OP_result_dropdowwn = (By.XPATH, '//*[@id="mfae908c1_tdrow_[C:10]_combobox-img[R:0]"]')
                safe_click(driver, OP_result_dropdowwn, timeout=20)

                # 3) click OP Result dropdown and select OK (open lookup)
                # wait until dropdown appears and OK button is clickable
                click_ok_locator = (By.XPATH, '//*[@id="menu0_OK_OPTION_a"]')
                logging.info(" Waiting for OK option to appear in dropdown...")

                # Wait specifically for the OK button to be present and clickable
                try:
                    ok_button = WebDriverWait(driver, 10).until(
                        EC.element_to_be_clickable(click_ok_locator)
                    )
                    logging.info(" OK option is now available")
                    time.sleep(0.5)  # Small additional wait to ensure dropdown is fully rendered

                    safe_click(driver, click_ok_locator, timeout=20)
                    logging.info(" Successfully clicked OK in OP Result dropdown")
                except TimeoutException:
                    logging.error(" OK option did not appear in dropdown within timeout")
                    screenshot_on_error(driver, prefix=f"ok_option_timeout_{work_order}")
                    raise
                
                # 4) navigate Labor tab
                labor_lookup_locator = (By.XPATH, '//*[@id="mc4588c78-tab_anchor"]')
                safe_click(driver, labor_lookup_locator, timeout=20)
                logging.info(" Navigated to Labor tab")

                # 5) click 'Select Planneed Labor'
                select_plan_labor_locator = (By.XPATH, '//*[@id="m4dfd8aef_bg_button_aclabor-pb"]')
                safe_click(driver, select_plan_labor_locator, timeout=20)
                logging.info(" Clicked Select Plan Labor")
                
                # 6) click first result in lookup
                first_result_locator = (By.XPATH, '//*[@id="mbdd0328d_tdrow_[C:0]_tbselrow-ti[R:0]_img"]')
                safe_click(driver, first_result_locator, timeout=20)
                logging.info(" Selected first lookup result")

                # 7) click OK
                list_view_locator = (By.XPATH, '//*[@id="m543bff42-pb"]')
                safe_click(driver, list_view_locator, timeout=20)
                logging.info(" Labor Click OK")

                # 8) enter start time - with robust waiting
                logging.info(" Entering start time...")
                start_time_locator = (By.XPATH, '//*[@id="m4dfd8aef_tdrow_[C:5]_txt-tb[R:0]"]')

                # Wait for field to be available and interactable
                start_time_field = WebDriverWait(driver, 20).until(
                    EC.element_to_be_clickable(start_time_locator)
                )

                # Focus the field first
                start_time_field.click()
                time.sleep(0.5)

                # Clear and enter value
                safe_send_keys(driver, start_time_locator, "06:00:00", clear_first=True, timeout=20)

                # Verify the value was entered by checking the field value
                try:
                    entered_value = start_time_field.get_attribute("value")
                    if "06:00:00" in entered_value:
                        logging.info(f" Start time entered successfully: {entered_value}")
                    else:
                        logging.warning(f" Start time may not have been entered correctly: {entered_value}")
                except Exception as e:
                    logging.warning(f" Could not verify start time entry: {e}")

                # Move focus away to trigger any field validation/processing
                start_time_field.send_keys(Keys.TAB)
                time.sleep(1)

                # 9) enter end date - with robust waiting
                logging.info(" Entering end date...")
                end_date_locator = (By.XPATH, '//*[@id="m4dfd8aef_tdrow_[C:6]_txt-tb[R:0]"]')

                # Wait for field to be available and interactable
                end_date_field = WebDriverWait(driver, 20).until(
                    EC.element_to_be_clickable(end_date_locator)
                )

                # Focus the field first
                end_date_field.click()
                time.sleep(0.5)

                # Clear and enter value
                safe_send_keys(driver, end_date_locator, "2025-08-20", clear_first=True, timeout=20)

                # Verify the value was entered
                try:
                    entered_value = end_date_field.get_attribute("value")
                    if "2025-08-20" in entered_value:
                        logging.info(f" End date entered successfully: {entered_value}")
                    else:
                        logging.warning(f" End date may not have been entered correctly: {entered_value}")
                except Exception as e:
                    logging.warning(f" Could not verify end date entry: {e}")

                # Move focus away to trigger any field validation/processing
                end_date_field.send_keys(Keys.TAB)
                time.sleep(1)

                # 10) enter end time - with robust waiting
                logging.info(" Entering end time...")
                end_time_locator = (By.XPATH, '//*[@id="m4dfd8aef_tdrow_[C:7]_txt-tb[R:0]"]')

                # Wait for field to be available and interactable
                end_time_field = WebDriverWait(driver, 20).until(
                    EC.element_to_be_clickable(end_time_locator)
                )

                # Focus the field first
                end_time_field.click()
                time.sleep(0.5)

                # Clear and enter value
                safe_send_keys(driver, end_time_locator, "10:00:00", clear_first=True, timeout=20)

                # Verify the value was entered
                try:
                    entered_value = end_time_field.get_attribute("value")
                    if "10:00:00" in entered_value:
                        logging.info(f" End time entered successfully: {entered_value}")
                    else:
                        logging.warning(f" End time may not have been entered correctly: {entered_value}")
                except Exception as e:
                    logging.warning(f" Could not verify end time entry: {e}")

                # Move focus away to trigger any field validation/processing
                end_time_field.send_keys(Keys.TAB)
                time.sleep(1)

                logging.info(" All date/time fields completed")

                # 11) click list view
                list_view_locator = (By.XPATH, '//*[@id="m397b0593-tabs_middle"]')
                safe_click(driver, list_view_locator, timeout=20)

                # 7) click save
                save_button_locator = (By.XPATH, '//*[@id="me1720906-pb"]')
                safe_click(driver, save_button_locator, timeout=30)
                logging.info(" Clicked Save")

                # final small wait to ensure save completes
                time.sleep(2)

                success = True
                break  # break out of retry loop
            except Exception as e:
                logging.error(f"Attempt {attempt} failed for WO {work_order}: {e}", exc_info=True)
                screenshot_on_error(driver, prefix=f"wo_{work_order}_attempt{attempt}")
                # try to recover by refreshing small or re-navigating to WOTRACK icon if needed
                try:
                    logging.info("Attempting small recovery: closing dialogs and re-finding work order page...")
                    # You might want to close modal dialogs here if you know their selectors.
                    # As a general measure, try clicking the WOTRACK favorite again to ensure context
                    safe_click(driver, (By.XPATH, '//*[@id="FavoriteApp_WOTRACK"]'), timeout=10)
                except Exception:
                    logging.debug("Recovery click failed - will try next attempt")
                time.sleep(2 * attempt)  # incremental backoff
        if not success:
            logging.error(f"Failed to process Work Order {work_order} after {row_attempts} attempts. Continuing to next row.")

except Exception as e:
    logging.error(f"An (outer) error occurred: {e}", exc_info=True)
    try:
        screenshot_on_error(driver, prefix="outer_exception")
    except Exception:
        pass

finally:
    # Ask if the user wants to close the browser
    close_browser = input("Do you want to close the browser? (Y/N): ")
    if close_browser.upper() == 'Y':
        driver.quit()
        logging.info("Browser closed.")
    else:
        logging.info("Browser left open for inspection.")