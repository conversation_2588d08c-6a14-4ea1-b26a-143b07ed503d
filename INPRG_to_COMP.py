import pandas as pd
import os
import time
import pyperclip
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Web<PERSON>river<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Set up logging
import logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def debug_available_links(driver, work_order_no):
    """Debug function to print all available links on the page"""
    try:
        links = driver.find_elements(By.TAG_NAME, "a")
        logging.info(f"Found {len(links)} links on the page:")
        for i, link in enumerate(links[:10]):  # Show first 10 links
            try:
                text = link.text.strip()
                href = link.get_attribute("href")
                if text or href:
                    logging.info(f"  Link {i+1}: Text='{text}', Href='{href}'")
            except Exception as e:
                logging.warning(f"  Link {i+1}: Error getting info - {e}")

        # Look specifically for links containing the work order number
        wo_links = driver.find_elements(By.XPATH, f"//a[contains(text(), '{work_order_no}')]")
        logging.info(f"Found {len(wo_links)} links containing work order number '{work_order_no}'")

    except Exception as e:
        logging.error(f"Error in debug function: {e}")

def debug_available_elements(driver, element_type):
    """Debug function to print all available elements of a specific type"""
    try:
        if element_type == "input":
            elements = driver.find_elements(By.TAG_NAME, "input")
            logging.info(f"Found {len(elements)} input elements:")
            for i, elem in enumerate(elements[:15]):  # Show first 15 elements
                try:
                    elem_id = elem.get_attribute("id")
                    elem_class = elem.get_attribute("class")
                    elem_type = elem.get_attribute("type")
                    elem_placeholder = elem.get_attribute("placeholder")
                    logging.info(f"  Input {i+1}: ID='{elem_id}', Class='{elem_class}', Type='{elem_type}', Placeholder='{elem_placeholder}'")
                except Exception as e:
                    logging.warning(f"  Input {i+1}: Error getting info - {e}")

        elif element_type == "button":
            elements = driver.find_elements(By.TAG_NAME, "button")
            logging.info(f"Found {len(elements)} button elements:")
            for i, elem in enumerate(elements[:15]):  # Show first 15 elements
                try:
                    text = elem.text.strip()
                    elem_class = elem.get_attribute("class")
                    elem_title = elem.get_attribute("title")
                    logging.info(f"  Button {i+1}: Text='{text}', Class='{elem_class}', Title='{elem_title}'")
                except Exception as e:
                    logging.warning(f"  Button {i+1}: Error getting info - {e}")

    except Exception as e:
        logging.error(f"Error in debug_available_elements function: {e}")

# Define the path to the Excel file
excel_file_path = os.path.join(os.path.dirname(__file__), 'Work Order.xlsx')

# Load the Excel file
try:
    df = pd.read_excel(excel_file_path)
    logging.info(f"Successfully loaded {excel_file_path}")
    logging.info(f"Found {len(df)} work orders to create")
except Exception as e:
    logging.error(f"Error loading Excel file: {e}")
    exit(1)

# Initialize WebDriver
options = webdriver.ChromeOptions()
options.add_experimental_option("detach", True)  # Keep browser open for debugging

try:
    # Set up the Chrome driver
    driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)
    driver.maximize_window()

    # Navigate to the EMS website
    logging.info("Opening EMS website...")
    driver.get('https://ems2-ensol.singlex.com/')

    # Wait for user to log in manually if needed
    input("Please log in to the website if required, then press Enter to continue...")

    # Click on the sidebar menu
    logging.info("Navigating to Work Order creation...")
    sidebar_menu = WebDriverWait(driver, 30).until(
        EC.element_to_be_clickable((By.XPATH, '//*[@id="sidebarScrollBar"]/div[2]/ul/li[3]/div[1]/h5'))
    )
    sidebar_menu.click()

    # Click on the Work Order submenu
    work_order_menu = WebDriverWait(driver, 30).until(
        EC.element_to_be_clickable((By.XPATH, '//*[@id="sidebarScrollBar"]/div[2]/ul/li[3]/div[2]/ul/li[2]/a/span'))
    )
    work_order_menu.click()
    time.sleep(10)

    # Process each row in the Excel file
    for index, row in df.iterrows():
        try:
            logging.info(f"Processing row {index+1}/{len(df)}")

            work_order_no = str(df['Work Order No.'][index]).strip()
            print(f"Modify Work order- {work_order_no}...")

            # enter work order number
            work_order_number = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '//*[@id="root"]/div[2]/section/div/div[2]/div[2]/div/div[3]/div[1]/div[1]/div[1]/div/input'))
            )
            work_order_number.send_keys(work_order_no)
            work_order_number.send_keys(Keys.ENTER)
            time.sleep(2)  # Give more time for search results to load

            # Wait for search results to appear
            try:
                WebDriverWait(driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'ant-table-tbody')]"))
                )
                logging.info("Search results loaded successfully")
            except TimeoutException:
                logging.warning("Search results table not found, continuing anyway...")
                # Take a screenshot for debugging
                driver.save_screenshot(f"debug_search_results_{work_order_no}.png")
                logging.info(f"Screenshot saved as debug_search_results_{work_order_no}.png")

            # Click WO Number - try multiple strategies
            wo_number = None
            strategies = [
                # Strategy 1: Look for clickable link containing the work order number
                (By.XPATH, f"//a[contains(text(), '{work_order_no}')]"),
                # Strategy 2: Look for any clickable link in the results area
                (By.XPATH, "//div[contains(@class, 'ant-table-tbody')]//a"),
                # Strategy 3: Look for the first clickable link in the table row
                (By.XPATH, "//tr[contains(@class, 'ant-table-row')]//a"),
                # Strategy 4: Original XPath as fallback
                (By.XPATH, '//*[@id="root"]/div[2]/section/div/div[2]/div[2]/div/section/div/div[2]/div/div[32]/div[1]/div[1]/div[2]/div[1]/div/a'),
                # Strategy 5: More flexible XPath looking for any link in the results section
                (By.XPATH, "//section//div//a[contains(@href, 'work') or contains(@class, 'work')]")
            ]

            for i, (by_method, selector) in enumerate(strategies, 1):
                try:
                    logging.info(f"Trying strategy {i} to find WO number link...")
                    wo_number = WebDriverWait(driver, 10).until(
                        EC.element_to_be_clickable((by_method, selector))
                    )
                    logging.info(f"Strategy {i} successful!")
                    break
                except TimeoutException:
                    logging.warning(f"Strategy {i} failed, trying next...")
                    continue

            if wo_number is None:
                # Debug: Print available links to help troubleshoot
                logging.error("Could not find work order link. Debugging available links...")
                debug_available_links(driver, work_order_no)
                driver.save_screenshot(f"debug_no_link_found_{work_order_no}.png")
                raise Exception("Could not find clickable work order number link with any strategy")

            # Scroll element into view and click
            driver.execute_script("arguments[0].scrollIntoView(true);", wo_number)
            time.sleep(1)

            # Try clicking with different methods
            try:
                wo_number.click()
            except Exception as e:
                logging.warning(f"Regular click failed: {e}, trying JavaScript click...")
                driver.execute_script("arguments[0].click();", wo_number)

            time.sleep(2)

            # enter result - try multiple strategies
            result_field = None
            result_strategies = [
                # Strategy 1: Updated XPath you provided
                (By.XPATH, '/html/body/div[1]/div[2]/section/div/div[2]/div[2]/div[2]/div[2]/div[2]/div/section[3]/div[2]/div/div/div[2]/div/section/div/div[1]/div[12]/div[1]/div[1]/div[2]/div[9]/div/div/div/div/div/input'),
                # Strategy 2: Original XPath as fallback
                (By.XPATH, '//*[@id="popup-73811da8-730b-66f0-2a29-6139e2949bb0"]/div[2]/div[2]/div/section[3]/div[2]/div/div/div[2]/div/section/div/div[1]/div[12]/div[1]/div[1]/div[2]/div[9]/div/div/div/div/div/input'),
                # Strategy 3: Look for input field in result section
                (By.XPATH, "//div[contains(@class, 'result') or contains(text(), 'Result')]//input"),
                # Strategy 4: Look for any input field in the popup
                (By.XPATH, "//div[contains(@id, 'popup')]//input[contains(@class, 'ant-input')]"),
                # Strategy 5: More general approach - look for input fields in the work order form
                (By.XPATH, "//section//input[not(@type='hidden')]")
            ]

            for i, (by_method, selector) in enumerate(result_strategies, 1):
                try:
                    logging.info(f"Trying strategy {i} to find result input field...")
                    result_field = WebDriverWait(driver, 10).until(
                        EC.element_to_be_clickable((by_method, selector))
                    )
                    logging.info(f"Result field strategy {i} successful!")
                    break
                except TimeoutException:
                    logging.warning(f"Result field strategy {i} failed, trying next...")
                    continue

            if result_field is None:
                logging.error("Could not find result input field. Taking screenshot for debugging...")
                debug_available_elements(driver, "input")
                driver.save_screenshot(f"debug_result_field_{work_order_no}.png")
                raise Exception("Could not find result input field with any strategy")

            # Clear field and enter result
            result_field.clear()
            result_field.send_keys("OK")
            result_field.send_keys(Keys.ENTER)
            time.sleep(1)

            # go to labor tab - try multiple strategies
            labor_tab = None
            labor_tab_strategies = [
                # Strategy 1: Look for tab containing "Labor" text
                (By.XPATH, "//a[contains(text(), 'Labor') or contains(text(), 'labour')]//span"),
                # Strategy 2: Look for second tab in the tab list
                (By.XPATH, "//ul[contains(@class, 'ant-tabs')]//li[2]//a//span"),
                # Strategy 3: Original XPath as fallback
                (By.XPATH, '//*[@id="popup-73811da8-730b-66f0-2a29-6139e2949bb0"]/div[2]/div[2]/div/section[3]/div[2]/div/div/div[2]/div/div[4]/ul/li[2]/a/span'),
                # Strategy 4: More flexible approach
                (By.XPATH, "//div[contains(@id, 'popup')]//ul//li[2]//a"),
                # Strategy 5: Look for any tab with labor-related class or text
                (By.XPATH, "//li[contains(@class, 'tab') or contains(@role, 'tab')]//a[contains(text(), 'Labor')]")
            ]

            for i, (by_method, selector) in enumerate(labor_tab_strategies, 1):
                try:
                    logging.info(f"Trying strategy {i} to find labor tab...")
                    labor_tab = WebDriverWait(driver, 10).until(
                        EC.element_to_be_clickable((by_method, selector))
                    )
                    logging.info(f"Labor tab strategy {i} successful!")
                    break
                except TimeoutException:
                    logging.warning(f"Labor tab strategy {i} failed, trying next...")
                    continue

            if labor_tab is None:
                logging.error("Could not find labor tab. Taking screenshot for debugging...")
                driver.save_screenshot(f"debug_labor_tab_{work_order_no}.png")
                raise Exception("Could not find labor tab with any strategy")

            labor_tab.click()
            time.sleep(2)

            # Add Allocated labor - try multiple strategies
            add_labor_button = None
            add_labor_strategies = [
                # Strategy 1: Look for button containing "Add" text
                (By.XPATH, "//button[contains(text(), 'Add') and (contains(text(), 'Labor') or contains(text(), 'Allocated'))]"),
                # Strategy 2: Look for the third button in the button group
                (By.XPATH, "//div[contains(@class, 'button') or contains(@class, 'btn')]//button[3]"),
                # Strategy 3: Original XPath as fallback
                (By.XPATH, '//*[@id="popup-73811da8-730b-66f0-2a29-6139e2949bb0"]/div[2]/div[2]/div/section[3]/div[2]/div/div/div[2]/div/div[5]/div[2]/div/div/button[3]'),
                # Strategy 4: Look for any button with add icon or plus sign
                (By.XPATH, "//button[contains(@class, 'add') or contains(text(), '+') or contains(@title, 'Add')]"),
                # Strategy 5: More flexible approach in the labor section
                (By.XPATH, "//div[contains(@id, 'popup')]//button[contains(text(), 'Add') or position()=3]")
            ]

            for i, (by_method, selector) in enumerate(add_labor_strategies, 1):
                try:
                    logging.info(f"Trying strategy {i} to find add labor button...")
                    add_labor_button = WebDriverWait(driver, 10).until(
                        EC.element_to_be_clickable((by_method, selector))
                    )
                    logging.info(f"Add labor button strategy {i} successful!")
                    break
                except TimeoutException:
                    logging.warning(f"Add labor button strategy {i} failed, trying next...")
                    continue

            if add_labor_button is None:
                logging.error("Could not find add labor button. Taking screenshot for debugging...")
                driver.save_screenshot(f"debug_add_labor_button_{work_order_no}.png")
                raise Exception("Could not find add labor button with any strategy")

            add_labor_button.click()
            time.sleep(2)

            # double-click first row - try multiple strategies
            first_row = None
            first_row_strategies = [
                # Strategy 1: Look for first row in table body
                (By.XPATH, "//div[contains(@class, 'ant-table-tbody')]//div[contains(@class, 'ant-table-row')][1]"),
                # Strategy 2: Look for first clickable row in any table
                (By.XPATH, "//table//tr[contains(@class, 'row')][1]//td[1]"),
                # Strategy 3: Original XPath as fallback
                (By.XPATH, '//*[@id="popup-cc162d7a-5e6f-c7eb-2cf1-08dc00878f53"]/div[2]/div[2]/div/section/div/div[2]/div/div[6]/div[1]/div[1]/div[2]/div[2]'),
                # Strategy 4: Look for first data row in popup
                (By.XPATH, "//div[contains(@id, 'popup')]//div[contains(@class, 'row')][1]"),
                # Strategy 5: More flexible approach - first clickable element in table area
                (By.XPATH, "//div[contains(@class, 'table')]//div[contains(@class, 'row') or contains(@class, 'cell')][1]")
            ]

            for i, (by_method, selector) in enumerate(first_row_strategies, 1):
                try:
                    logging.info(f"Trying strategy {i} to find first row...")
                    first_row = WebDriverWait(driver, 10).until(
                        EC.element_to_be_clickable((by_method, selector))
                    )
                    logging.info(f"First row strategy {i} successful!")
                    break
                except TimeoutException:
                    logging.warning(f"First row strategy {i} failed, trying next...")
                    continue

            if first_row is None:
                logging.error("Could not find first row. Taking screenshot for debugging...")
                driver.save_screenshot(f"debug_first_row_{work_order_no}.png")
                raise Exception("Could not find first row with any strategy")

            # Scroll into view and double-click
            driver.execute_script("arguments[0].scrollIntoView(true);", first_row)
            time.sleep(1)

            # Try double-click with ActionChains for better reliability
            actions = ActionChains(driver)
            actions.double_click(first_row).perform()
            time.sleep(2)

            # fill in start Date - try multiple strategies
            start_date_field = None
            start_date_strategies = [
                # Strategy 1: Original ID
                (By.ID, 'DatePickerInGrid_3_0'),
                # Strategy 2: Look for date picker with start in name/class
                (By.XPATH, "//input[contains(@id, 'DatePicker') and contains(@id, '3')]"),
                # Strategy 3: Look for first date input field
                (By.XPATH, "//input[contains(@class, 'date') or contains(@type, 'date')][1]"),
                # Strategy 4: More flexible date picker search
                (By.XPATH, "//input[contains(@id, 'DatePicker')][1]"),
                # Strategy 5: Any input that looks like a date field
                (By.XPATH, "//input[contains(@placeholder, 'date') or contains(@class, 'picker')]")
            ]

            for i, (by_method, selector) in enumerate(start_date_strategies, 1):
                try:
                    logging.info(f"Trying strategy {i} to find start date field...")
                    start_date_field = WebDriverWait(driver, 10).until(
                        EC.element_to_be_clickable((by_method, selector))
                    )
                    logging.info(f"Start date field strategy {i} successful!")
                    break
                except TimeoutException:
                    logging.warning(f"Start date field strategy {i} failed, trying next...")
                    continue

            if start_date_field is None:
                logging.error("Could not find start date field. Taking screenshot for debugging...")
                driver.save_screenshot(f"debug_start_date_{work_order_no}.png")
                raise Exception("Could not find start date field with any strategy")

            start_date_field.clear()
            start_date_field.send_keys(str(row['Plan Start Date']))
            time.sleep(1)

            # fill in end date - try multiple strategies
            end_date_field = None
            end_date_strategies = [
                # Strategy 1: Original ID
                (By.ID, 'DatePickerInGrid_4_0'),
                # Strategy 2: Look for date picker with end in name/class
                (By.XPATH, "//input[contains(@id, 'DatePicker') and contains(@id, '4')]"),
                # Strategy 3: Look for second date input field
                (By.XPATH, "//input[contains(@class, 'date') or contains(@type, 'date')][2]"),
                # Strategy 4: More flexible date picker search
                (By.XPATH, "//input[contains(@id, 'DatePicker')][2]"),
                # Strategy 5: Look for end date specifically
                (By.XPATH, "//input[contains(@placeholder, 'end') or contains(@name, 'end')]")
            ]

            for i, (by_method, selector) in enumerate(end_date_strategies, 1):
                try:
                    logging.info(f"Trying strategy {i} to find end date field...")
                    end_date_field = WebDriverWait(driver, 10).until(
                        EC.element_to_be_clickable((by_method, selector))
                    )
                    logging.info(f"End date field strategy {i} successful!")
                    break
                except TimeoutException:
                    logging.warning(f"End date field strategy {i} failed, trying next...")
                    continue

            if end_date_field is None:
                logging.error("Could not find end date field. Taking screenshot for debugging...")
                driver.save_screenshot(f"debug_end_date_{work_order_no}.png")
                raise Exception("Could not find end date field with any strategy")

            end_date_field.clear()
            end_date_field.send_keys(str(row['Plan End Date']))
            time.sleep(1)

            # =================================
            # save work order
            save_button = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '/html/body/div[1]/div[2]/section/div/div[2]/div[2]/div[2]/div[2]/div[3]/div/button[1]'))
            )
            save_button.click()
            time.sleep(3)
            # press ok
            ok_button = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '/html/body/div[8]/div/div/div/div[3]/div/button'))
            )
            ok_button.click()
            time.sleep(3)

            # press close
            close_button = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '/html/body/div[1]/div[2]/section/div/div[2]/div[2]/div[2]/div[2]/div[3]/div/button[4]'))
            )
            close_button.click()
            time.sleep(3)


        except Exception as e:
            logging.error(f"Error processing row {index+1}: {e}")
            continue

    # Save the updated DataFrame back to Excel
    df.to_excel(excel_file_path, index=False)
    logging.info(f"Successfully saved work order numbers to {excel_file_path}")

except Exception as e:
    logging.error(f"An error occurred: {e}")

finally:
    # Ask if the user wants to close the browser
    close_browser = input("Do you want to close the browser? (Y/N): ")
    if close_browser.upper() == 'Y':
        driver.quit()
        logging.info("Browser closed.")
    else:
        logging.info("Browser left open for inspection.")
