import pandas as pd
import os
import time
import pyperclip
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Set up logging
import logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Define the path to the Excel file
excel_file_path = os.path.join(os.path.dirname(__file__), 'Work Order.xlsx')

# Load the Excel file
try:
    df = pd.read_excel(excel_file_path)
    logging.info(f"Successfully loaded {excel_file_path}")
    logging.info(f"Found {len(df)} work orders to create")
except Exception as e:
    logging.error(f"Error loading Excel file: {e}")
    exit(1)

# Initialize WebDriver
options = webdriver.ChromeOptions()
options.add_experimental_option("detach", True)  # Keep browser open for debugging

try:
    # Set up the Chrome driver
    driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)
    driver.maximize_window()

    # Navigate to the EMS website
    logging.info("Opening EMS website...")
    driver.get('https://ems2-ensol.singlex.com/')

    # Wait for user to log in manually if needed
    input("Please log in to the website if required, then press Enter to continue...")

    # Click on the sidebar menu
    logging.info("Navigating to Work Order creation...")
    sidebar_menu = WebDriverWait(driver, 30).until(
        EC.element_to_be_clickable((By.XPATH, '//*[@id="sidebarScrollBar"]/div[2]/ul/li[3]/div[1]/h5'))
    )
    sidebar_menu.click()

    # Click on the Work Order submenu
    work_order_menu = WebDriverWait(driver, 30).until(
        EC.element_to_be_clickable((By.XPATH, '//*[@id="sidebarScrollBar"]/div[2]/ul/li[3]/div[2]/ul/li[2]/a/span'))
    )
    work_order_menu.click()
    time.sleep(10)

    # Process each row in the Excel file
    for index, row in df.iterrows():
        try:
            logging.info(f"Processing row {index+1}/{len(df)}")

            work_order_no = str(df['Work Order No.'][index]).strip()
            print(f"Modify Work order- {work_order_no}...")

            # enter work order number
            work_order_number = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '//*[@id="root"]/div[2]/section/div/div[2]/div[2]/div/div[3]/div[1]/div[1]/div[1]/div/input'))
            )
            work_order_number.clear()
            work_order_number.send_keys(work_order_no)
            work_order_number.send_keys(Keys.ENTER)
            time.sleep(3)  # Wait for search results to load

            # Wait for any loading overlays to disappear
            try:
                WebDriverWait(driver, 10).until_not(
                    EC.presence_of_element_located((By.XPATH, "//div[contains(@class, 'sc-bdvvtL') or contains(@class, 'loading') or contains(@class, 'spinner')]"))
                )
                logging.info("Loading overlay disappeared")
            except TimeoutException:
                logging.info("No loading overlay found or it didn't disappear, continuing...")

            # Click WO Number with multiple strategies
            wo_number = None
            click_strategies = [
                # Strategy 1: Original XPath
                '//*[@id="root"]/div[2]/section/div/div[2]/div[2]/div/section/div/div[2]/div/div[32]/div[1]/div[1]/div[2]/div[1]/div/a',
                # Strategy 2: Look for any link containing the work order number
                f"//a[contains(text(), '{work_order_no}')]",
                # Strategy 3: Look for clickable link in search results
                "//div[contains(@class, 'ant-table')]//a[contains(@class, 'cursor-hand')]"
            ]

            for i, xpath in enumerate(click_strategies, 1):
                try:
                    logging.info(f"Trying click strategy {i}...")
                    wo_number = WebDriverWait(driver, 10).until(
                        EC.element_to_be_clickable((By.XPATH, xpath))
                    )

                    # Scroll element into view
                    driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth', block: 'center'});", wo_number)
                    time.sleep(1)

                    # Try different click methods
                    try:
                        # Method 1: Regular click
                        wo_number.click()
                        logging.info(f"Strategy {i} - Regular click successful!")
                        break
                    except Exception as e:
                        logging.warning(f"Strategy {i} - Regular click failed: {e}, trying JavaScript click...")
                        try:
                            # Method 2: JavaScript click
                            driver.execute_script("arguments[0].click();", wo_number)
                            logging.info(f"Strategy {i} - JavaScript click successful!")
                            break
                        except Exception as e2:
                            logging.warning(f"Strategy {i} - JavaScript click failed: {e2}, trying ActionChains...")
                            try:
                                # Method 3: ActionChains click
                                actions = ActionChains(driver)
                                actions.move_to_element(wo_number).click().perform()
                                logging.info(f"Strategy {i} - ActionChains click successful!")
                                break
                            except Exception as e3:
                                logging.warning(f"Strategy {i} - ActionChains click failed: {e3}")
                                if i == len(click_strategies):
                                    raise Exception("All click methods failed")
                                continue

                except TimeoutException:
                    logging.warning(f"Strategy {i} failed to find element, trying next...")
                    continue

            time.sleep(3)  # Wait for page to load after click

            # enter result - handle combobox properly
            result_strategies = [
                # Strategy 1: Look for combobox input
                "//input[@role='combobox' and contains(@class, 'wj-form-control')]",
                # Strategy 2: Original XPath
                '//*[@id="popup-73811da8-730b-66f0-2a29-6139e2949bb0"]/div[2]/div[2]/div/section[3]/div[2]/div/div/div[2]/div/section/div/div[1]/div[12]/div[1]/div[1]/div[2]/div[9]/div/div/div/div/div/input',
                # Strategy 3: Look for Wijmo input
                "//div[contains(@class, 'wj-input')]//input[@type='text']",
                # Strategy 4: Any input in the popup
                "//div[contains(@id, 'popup')]//input[not(@type='hidden')]"
            ]

            result_field = None
            for i, xpath in enumerate(result_strategies, 1):
                try:
                    logging.info(f"Trying result field strategy {i}...")
                    result_field = WebDriverWait(driver, 10).until(
                        EC.element_to_be_clickable((By.XPATH, xpath))
                    )
                    logging.info(f"Result field strategy {i} successful!")
                    break
                except TimeoutException:
                    logging.warning(f"Result field strategy {i} failed, trying next...")
                    continue

            if result_field is None:
                logging.error("Could not find result field")
                driver.save_screenshot(f"debug_result_field_{work_order_no}.png")
                raise Exception("Could not find result field with any strategy")

            # Handle the combobox interaction
            logging.info("Interacting with result combobox...")

            # Click to focus and activate dropdown
            result_field.click()
            time.sleep(1)

            # Clear any existing value
            result_field.clear()
            time.sleep(0.5)

            # Type "OK" to filter/select
            result_field.send_keys("OK")
            time.sleep(1)

            # Try to confirm selection
            try:
                # Look for dropdown option and click it
                ok_option = WebDriverWait(driver, 5).until(
                    EC.element_to_be_clickable((By.XPATH, "//div[contains(@class, 'wj-listbox-item') or contains(@class, 'dropdown-item')][contains(text(), 'OK')]"))
                )
                ok_option.click()
                logging.info("Clicked OK option in dropdown")
            except TimeoutException:
                # Fallback to keyboard navigation
                logging.info("Dropdown option not found, using keyboard...")
                result_field.send_keys(Keys.ENTER)
                time.sleep(0.5)
                # Alternative: try Tab to confirm
                result_field.send_keys(Keys.TAB)

            time.sleep(1)

            # go to labor tab
            labor_tab = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '//*[@id="popup-73811da8-730b-66f0-2a29-6139e2949bb0"]/div[2]/div[2]/div/section[3]/div[2]/div/div/div[2]/div/div[4]/ul/li[2]/a/span'))
            )
            labor_tab.click()

            # Add Allocated labor
            add_labor = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '//*[@id="popup-73811da8-730b-66f0-2a29-6139e2949bb0"]/div[2]/div[2]/div/section[3]/div[2]/div/div/div[2]/div/div[5]/div[2]/div/div/button[3]'))
            )
            add_labor.click()

            # double-click first row
            first_row = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '//*[@id="popup-cc162d7a-5e6f-c7eb-2cf1-08dc00878f53"]/div[2]/div[2]/div/section/div/div[2]/div/div[6]/div[1]/div[1]/div[2]/div[2]'))
            )
            first_row.double_click()

            # fill in start Date
            start_date = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '//*[@id="DatePickerInGrid_3_0"]'))
            )
            start_date.send_keys(row['Plan Start Date'])

            # fill in end date
            end_date = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '//*[@id="DatePickerInGrid_4_0"]'))
            )
            end_date.send_keys(row['Plan End Date'])

            # =================================
            # save work order
            save_button = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '/html/body/div[1]/div[2]/section/div/div[2]/div[2]/div[2]/div[2]/div[3]/div/button[1]'))
            )
            save_button.click()
            time.sleep(3)
            # press ok
            ok_button = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '/html/body/div[8]/div/div/div/div[3]/div/button'))
            )
            ok_button.click()
            time.sleep(3)

            # press close
            close_button = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '/html/body/div[1]/div[2]/section/div/div[2]/div[2]/div[2]/div[2]/div[3]/div/button[4]'))
            )
            close_button.click()
            time.sleep(3)


        except Exception as e:
            logging.error(f"Error processing row {index+1}: {e}")
            continue

    # Save the updated DataFrame back to Excel
    df.to_excel(excel_file_path, index=False)
    logging.info(f"Successfully saved work order numbers to {excel_file_path}")

except Exception as e:
    logging.error(f"An error occurred: {e}")

finally:
    # Ask if the user wants to close the browser
    close_browser = input("Do you want to close the browser? (Y/N): ")
    if close_browser.upper() == 'Y':
        driver.quit()
        logging.info("Browser closed.")
    else:
        logging.info("Browser left open for inspection.")
