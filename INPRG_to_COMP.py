import pandas as pd
import os
import time
import pyperclip
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Set up logging
import logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Define the path to the Excel file
excel_file_path = os.path.join(os.path.dirname(__file__), 'Work Order.xlsx')

# Load the Excel file
try:
    df = pd.read_excel(excel_file_path)
    logging.info(f"Successfully loaded {excel_file_path}")
    logging.info(f"Found {len(df)} work orders to create")
except Exception as e:
    logging.error(f"Error loading Excel file: {e}")
    exit(1)

# Initialize WebDriver
options = webdriver.ChromeOptions()
options.add_experimental_option("detach", True)  # Keep browser open for debugging

try:
    # Set up the Chrome driver
    driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)
    driver.maximize_window()

    # Navigate to the EMS website
    logging.info("Opening EMS website...")
    driver.get('https://ems2-ensol.singlex.com/')

    # Wait for user to log in manually if needed
    input("Please log in to the website if required, then press Enter to continue...")

    # Click on the sidebar menu
    logging.info("Navigating to Work Order creation...")
    sidebar_menu = WebDriverWait(driver, 30).until(
        EC.element_to_be_clickable((By.XPATH, '//*[@id="sidebarScrollBar"]/div[2]/ul/li[3]/div[1]/h5'))
    )
    sidebar_menu.click()

    # Click on the Work Order submenu
    work_order_menu = WebDriverWait(driver, 30).until(
        EC.element_to_be_clickable((By.XPATH, '//*[@id="sidebarScrollBar"]/div[2]/ul/li[3]/div[2]/ul/li[2]/a/span'))
    )
    work_order_menu.click()
    time.sleep(10)

    # Process each row in the Excel file
    for index, row in df.iterrows():
        try:
            logging.info(f"Processing row {index+1}/{len(df)}")

            work_order_no = str(df['Work Order No.'][index]).strip()
            print(f"Modify Work order- {work_order_no}...")

            # enter work order number
            work_order_number = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '//*[@id="root"]/div[2]/section/div/div[2]/div[2]/div/div[3]/div[1]/div[1]/div[1]/div/input'))
            )
            work_order_number.send_keys(work_order_no)
            work_order_number.send_keys(Keys.ENTER)
            time.sleep(0.5)

            # CLick WO Number
            wo_number = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '//*[@id="root"]/div[2]/section/div/div[2]/div[5]/div/section/div/div[2]/div/div[32]/div[1]/div[1]/div[2]/div[1]/div/a'))
            )
            wo_number.click()
            time.sleep(2)

            # enter result
            result = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '//*[@id="popup-73811da8-730b-66f0-2a29-6139e2949bb0"]/div[2]/div[2]/div/section[3]/div[2]/div/div/div[2]/div/section/div/div[1]/div[12]/div[1]/div[1]/div[2]/div[9]/div/div/div/div/div/input'))
            )
            result.send_keys("OK")
            result.send_keys(Keys.ENTER)
            time.sleep(0.1)

            # go to labor tab
            labor_tab = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '//*[@id="popup-73811da8-730b-66f0-2a29-6139e2949bb0"]/div[2]/div[2]/div/section[3]/div[2]/div/div/div[2]/div/div[4]/ul/li[2]/a/span'))
            )
            labor_tab.click()

            # Add Allocated labor
            add_labor = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '//*[@id="popup-73811da8-730b-66f0-2a29-6139e2949bb0"]/div[2]/div[2]/div/section[3]/div[2]/div/div/div[2]/div/div[5]/div[2]/div/div/button[3]'))
            )
            add_labor.click()

            # double-click first row
            first_row = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '//*[@id="popup-cc162d7a-5e6f-c7eb-2cf1-08dc00878f53"]/div[2]/div[2]/div/section/div/div[2]/div/div[6]/div[1]/div[1]/div[2]/div[2]'))
            )
            first_row.double_click()

            # fill in start Date
            start_date = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '//*[@id="DatePickerInGrid_3_0"]'))
            )
            start_date.send_keys(row['Plan Start Date'])

            # fill in end date
            end_date = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '//*[@id="DatePickerInGrid_4_0"]'))
            )
            end_date.send_keys(row['Plan End Date'])

            # =================================
            # save work order
            save_button = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '/html/body/div[1]/div[2]/section/div/div[2]/div[2]/div[2]/div[2]/div[3]/div/button[1]'))
            )
            save_button.click()
            time.sleep(3)
            # press ok
            ok_button = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '/html/body/div[8]/div/div/div/div[3]/div/button'))
            )
            ok_button.click()
            time.sleep(3)

            # press close
            close_button = WebDriverWait(driver, 30).until(
                EC.element_to_be_clickable((By.XPATH, '/html/body/div[1]/div[2]/section/div/div[2]/div[2]/div[2]/div[2]/div[3]/div/button[4]'))
            )
            close_button.click()
            time.sleep(3)


        except Exception as e:
            logging.error(f"Error processing row {index+1}: {e}")
            continue

    # Save the updated DataFrame back to Excel
    df.to_excel(excel_file_path, index=False)
    logging.info(f"Successfully saved work order numbers to {excel_file_path}")

except Exception as e:
    logging.error(f"An error occurred: {e}")

finally:
    # Ask if the user wants to close the browser
    close_browser = input("Do you want to close the browser? (Y/N): ")
    if close_browser.upper() == 'Y':
        driver.quit()
        logging.info("Browser closed.")
    else:
        logging.info("Browser left open for inspection.")
